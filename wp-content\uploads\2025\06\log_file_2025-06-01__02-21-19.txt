
---Downloaded files---
The import files for: restan Demo were successfully downloaded!
Initial max execution time = 120
Files info:
Site URL = http://localhost/restan
Data file = C:\xampp\htdocs\restan/wp-content/uploads/2025/06/demo-content-import-file_2025-06-01__02-21-19.xml
Widget file = C:\xampp\htdocs\restan/wp-content/uploads/2025/06/demo-widgets-import-file_2025-06-01__02-21-19.json
Customizer file = not defined!
Redux files:
restan_opt -> C:\xampp\htdocs\restan/wp-content/themes/restan//inc/demo-data/redux_options_demo.json 


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


------
New AJAX call!


---Importing widgets---
Blog Sidebar : 

Block - No Title - Widget already exists
restan Recent Posts - Recent Post - Imported

Page Sidebar : 

Block - No Title - Widget already exists
Block - No Title - Widget already exists



---Importing Redux settings---
Redux settings import for: restan_opt finished successfully!

---ocdi/after_import---

