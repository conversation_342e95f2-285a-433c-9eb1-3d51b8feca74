<?php // @codingStandardsIgnoreLine.
/**
 * WooCommerce Checkout Settings
 *
 * @package WooCommerce\Admin
 */

use Automattic\WooCommerce\Admin\Features\OnboardingTasks\Tasks\WooCommercePayments;
use Automattic\WooCommerce\Admin\Features\PaymentGatewaySuggestions\Init;
use Automattic\WooCommerce\Admin\PluginsHelper;

defined( 'ABSPATH' ) || exit;

if ( class_exists( 'WC_Settings_Payment_Gateways', false ) ) {
	return new WC_Settings_Payment_Gateways();
}

/**
 * WC_Settings_Payment_Gateways.
 */
class WC_Settings_Payment_Gateways extends WC_Settings_Page {

	/**
	 * Constructor.
	 */
	public function __construct() {
		$this->id    = 'checkout'; // @todo In future versions this may make more sense as 'payment' however to avoid breakage lets leave this alone until we refactor settings APIs in general.
		$this->label = _x( 'Payments', 'Settings tab label', 'woocommerce' );

		add_action( 'woocommerce_admin_field_payment_gateways_banner', array( $this, 'payment_gateways_banner' ) );
		add_action( 'woocommerce_admin_field_payment_gateways', array( $this, 'payment_gateways_setting' ) );
		parent::__construct();
	}

	/**
	 * Setting page icon.
	 *
	 * @var string
	 */
	public $icon = 'payment';

	/**
	 * Get own sections.
	 *
	 * @return array
	 */
	protected function get_own_sections() {
		return array(
			'' => __( 'Payment methods', 'woocommerce' ),
		);
	}

	/**
	 * Get settings array.
	 *
	 * @return array
	 */
	protected function get_settings_for_default_section() {
		$settings =
			array(
				array(
					'type' => 'title', // this is needed as <table> tag is generated by this element, even if it has no other content.
				),
				array( 'type' => 'payment_gateways_banner' ), // React mount point for embedded banner slotfill.
				array(
					'type' => 'payment_gateways',
				),
				array(
					'type' => 'sectionend',
					'id'   => 'payment_gateways_options',
				),
			);

		return apply_filters( 'woocommerce_payment_gateways_settings', $settings );
	}

	/**
	 * Output the settings.
	 */
	public function output() {
		//phpcs:disable WordPress.Security.NonceVerification.Recommended
		global $current_section;

		// Load gateways so we can show any global options they may have.
		$payment_gateways = WC()->payment_gateways->payment_gateways();

		if ( $current_section ) {
			foreach ( $payment_gateways as $gateway ) {
				if ( in_array( $current_section, array( $gateway->id, sanitize_title( get_class( $gateway ) ) ), true ) ) {
					if ( isset( $_GET['toggle_enabled'] ) ) {
						$enabled = $gateway->get_option( 'enabled' );

						if ( $enabled ) {
							$gateway->settings['enabled'] = wc_string_to_bool( $enabled ) ? 'no' : 'yes';
						}
					}
					$this->run_gateway_admin_options( $gateway );
					break;
				}
			}
		}

		parent::output();
		//phpcs:enable
	}

	/**
	 * Run the 'admin_options' method on a given gateway.
	 * This method exists to easy unit testing.
	 *
	 * @param object $gateway The gateway object to run the method on.
	 */
	protected function run_gateway_admin_options( $gateway ) {
		$gateway->admin_options();
	}

	/**
	 * Creates the React mount point for the embedded banner.
	 */
	public function payment_gateways_banner() {
		?>
		<div id="wc_payments_settings_slotfill"> </div>
		<?php
	}

	/**
	 * Output payment gateway settings.
	 */
	public function payment_gateways_setting() {
		?>
		<tr valign="top">
		<td class="wc_payment_gateways_wrapper" colspan="2">
			<table class="wc_gateways widefat" cellspacing="0" aria-describedby="payment_gateways_options-description">
				<thead>
					<tr>
						<?php
						$default_columns = array(
							'sort'        => '',
							'name'        => __( 'Method', 'woocommerce' ),
							'status'      => __( 'Enabled', 'woocommerce' ),
							'description' => __( 'Description', 'woocommerce' ),
							'action'      => '',
						);

						$columns = apply_filters( 'woocommerce_payment_gateways_setting_columns', $default_columns );

						foreach ( $columns as $key => $column ) {
							echo '<th class="' . esc_attr( $key ) . '">' . esc_html( $column ) . '</th>';
						}
						?>
						</tr>
					</thead>
					<tbody>
						<?php
						$payment_gateways = WC()->payment_gateways->payment_gateways();
						foreach ( $payment_gateways as $gateway ) {

							echo '<tr data-gateway_id="' . esc_attr( $gateway->id ) . '">';

							foreach ( $columns as $key => $column ) {
								if ( ! array_key_exists( $key, $default_columns ) ) {
									do_action( 'woocommerce_payment_gateways_setting_column_' . $key, $gateway );
									continue;
								}

								$width = '';

								if ( in_array( $key, array( 'sort', 'status', 'action' ), true ) ) {
									$width = '1%';
								}

								$method_title = $gateway->get_method_title() ? $gateway->get_method_title() : $gateway->get_title();
								$custom_title = $gateway->get_title();

								echo '<td class="' . esc_attr( $key ) . '" width="' . esc_attr( $width ) . '">';

								switch ( $key ) {
									case 'sort':
										?>
										<div class="wc-item-reorder-nav">
											<button type="button" class="wc-move-up" tabindex="0" aria-hidden="false" aria-label="<?php /* Translators: %s Payment gateway name. */ echo esc_attr( sprintf( __( 'Move the "%s" payment method up', 'woocommerce' ), $method_title ) ); ?>"><?php esc_html_e( 'Move up', 'woocommerce' ); ?></button>
											<button type="button" class="wc-move-down" tabindex="0" aria-hidden="false" aria-label="<?php /* Translators: %s Payment gateway name. */ echo esc_attr( sprintf( __( 'Move the "%s" payment method down', 'woocommerce' ), $method_title ) ); ?>"><?php esc_html_e( 'Move down', 'woocommerce' ); ?></button>
											<input type="hidden" name="gateway_order[]" value="<?php echo esc_attr( $gateway->id ); ?>" />
										</div>
										<?php
										break;
									case 'name':
										echo '<a href="' . esc_url( admin_url( 'admin.php?page=wc-settings&tab=checkout&section=' . strtolower( $gateway->id ) ) ) . '" class="wc-payment-gateway-method-title">' . wp_kses_post( $method_title ) . '</a>';

										if ( $method_title !== $custom_title ) {
											echo '<span class="wc-payment-gateway-method-name">&nbsp;&ndash;&nbsp;' . wp_kses_post( $custom_title ) . '</span>';
										}
										break;
									case 'description':
										echo wp_kses_post( $gateway->get_method_description() );
										break;
									case 'action':
										$setup_url = admin_url( 'admin.php?page=wc-settings&tab=checkout&section=' . strtolower( $gateway->id ) );
										// Override the behaviour for the WooPayments plugin.
										if (
											// Keep old brand name for backwards compatibility.
											( 'WooCommerce Payments' === $method_title || 'WooPayments' === $method_title ) &&
											class_exists( 'WC_Payments_Account' )
										) {
											if ( ! WooCommercePayments::is_connected() || WooCommercePayments::is_account_partially_onboarded() ) {
												// The CTA text and label is "Finish setup" if the account is not connected or not completely onboarded.
												// Plugin will handle the redirection to the connect page or directly to the provider (e.g. Stripe).
												$setup_url = WC_Payments_Account::get_connect_url();
												// Add the `from` parameter to the URL, so we know where the user came from.
												$setup_url = add_query_arg( 'from', 'WCADMIN_PAYMENT_SETTINGS', $setup_url );
												/* Translators: %s Payment gateway name. */
												echo '<a class="button alignright" aria-label="' . esc_attr( sprintf( __( 'Set up the "%s" payment method', 'woocommerce' ), $method_title ) ) . '" href="' . esc_url( $setup_url ) . '">' . esc_html__( 'Finish setup', 'woocommerce' ) . '</a>';
											} else {
												// If the account is fully onboarded, the CTA text and label is "Manage" regardless gateway is enabled or not.
												/* Translators: %s Payment gateway name. */
												echo '<a class="button alignright" aria-label="' . esc_attr( sprintf( __( 'Manage the "%s" payment method', 'woocommerce' ), $method_title ) ) . '" href="' . esc_url( $setup_url ) . '">' . esc_html__( 'Manage', 'woocommerce' ) . '</a>';
											}
										} elseif ( wc_string_to_bool( $gateway->enabled ) ) {
											/* Translators: %s Payment gateway name. */
											echo '<a class="button alignright" aria-label="' . esc_attr( sprintf( __( 'Manage the "%s" payment method', 'woocommerce' ), $method_title ) ) . '" href="' . esc_url( $setup_url ) . '">' . esc_html__( 'Manage', 'woocommerce' ) . '</a>';
										} else {
											/* Translators: %s Payment gateway name. */
											echo '<a class="button alignright" aria-label="' . esc_attr( sprintf( __( 'Set up the "%s" payment method', 'woocommerce' ), $method_title ) ) . '" href="' . esc_url( $setup_url ) . '">' . esc_html__( 'Finish setup', 'woocommerce' ) . '</a>';
										}
										break;
									case 'status':
										echo '<a class="wc-payment-gateway-method-toggle-enabled" href="' . esc_url( admin_url( 'admin.php?page=wc-settings&tab=checkout&section=' . strtolower( $gateway->id ) ) ) . '">';
										if ( wc_string_to_bool( $gateway->enabled ) ) {
											/* Translators: %s Payment gateway name. */
											echo '<span class="woocommerce-input-toggle woocommerce-input-toggle--enabled" aria-label="' . esc_attr( sprintf( __( 'The "%s" payment method is currently enabled', 'woocommerce' ), $method_title ) ) . '">' . esc_attr__( 'Yes', 'woocommerce' ) . '</span>';
										} else {
											/* Translators: %s Payment gateway name. */
											echo '<span class="woocommerce-input-toggle woocommerce-input-toggle--disabled" aria-label="' . esc_attr( sprintf( __( 'The "%s" payment method is currently disabled', 'woocommerce' ), $method_title ) ) . '">' . esc_attr__( 'No', 'woocommerce' ) . '</span>';
										}
										echo '</a>';
										break;
								}

								echo '</td>';
							}

							echo '</tr>';
						}
						/**
						 * Add "Other payment methods" link in WooCommerce -> Settings -> Payments
						 * When the store is in WC Payments eligible country.
						 * See https://github.com/woocommerce/woocommerce/issues/32130 for more details.
						 */
						if ( WooCommercePayments::is_supported() ) {
							$wcpay_setup        = isset( $payment_gateways['woocommerce_payments'] ) && ! $payment_gateways['woocommerce_payments']->needs_setup();
							$country            = wc_get_base_location()['country'];
							$plugin_suggestions = Init::get_suggestions();
							$active_plugins     = PluginsHelper::get_active_plugin_slugs();

							if ( $wcpay_setup ) {
								$filter_by = 'category_additional';
							} else {
								$filter_by = 'category_other';
							}

							$marketplace_cta_allowed_html = array(
								'a' => array(
									'href'  => array(),
									'id'    => array(),
									'style' => array(),
								),
							);

							$marketplace_cta = sprintf(
								wp_kses(
									/* translators: %s: URL to WooCommerce marketplace */
									__( 'Visit the <a href="%s" id="settings-other-payment-methods" style="text-decoration: underline;">Official WooCommerce Marketplace</a> to find additional payment providers.', 'woocommerce' ),
									$marketplace_cta_allowed_html
								),
								esc_url( admin_url( 'admin.php?page=wc-admin&tab=extensions&path=/extensions&category=payment-gateways' ) )
							);

							$plugin_suggestions = array_filter(
								$plugin_suggestions,
								function( $plugin ) use ( $country, $filter_by, $active_plugins ) {
									if ( ! isset( $plugin->{$filter_by} ) || ! isset( $plugin->image_72x72 ) || ! isset( $plugin->plugins[0] ) || in_array( $plugin->plugins[0], $active_plugins, true ) ) {
										return false;
									}
									return in_array( $country, $plugin->{$filter_by}, true );
								}
							);

							$columns_count = count( $columns );

							echo '<tr>';
							// phpcs:ignore -- ignoring the error since the value is harded.
							echo "<td style='font-size: 13px; border-top: 1px solid #c3c4c7; background-color: #fff' colspan='{$columns_count}'>";
							echo '<span style="margin-right: 10px;">';
							echo wp_kses( $marketplace_cta, $marketplace_cta_allowed_html );
							echo '</span>';
							if ( count( $plugin_suggestions ) ) {
								foreach ( $plugin_suggestions as $plugin_suggestion ) {
									$alt = str_replace( '.png', '', basename( $plugin_suggestion->image_72x72 ) );
									// phpcs:ignore
									echo "<img src='{$plugin_suggestion->image_72x72}' alt='{$alt}' width='24' height='24' style='vertical-align: middle; margin-right: 8px;'/>";
								}
								echo '& more.';
							}
							echo '</td>';
							echo '</tr>';
						}
						?>
					</tbody>
				</table>
			</td>
		</tr>
		<?php
	}

	/**
	 * Save settings.
	 */
	public function save() {
		global $current_section;

		$wc_payment_gateways = WC_Payment_Gateways::instance();

		$this->save_settings_for_current_section();

		if ( ! $current_section ) {
			// If section is empty, we're on the main settings page. This makes sure 'gateway ordering' is saved.
			$wc_payment_gateways->process_admin_options();
			$wc_payment_gateways->init();
		} else {
			// There is a section - this may be a gateway or custom section.
			foreach ( $wc_payment_gateways->payment_gateways() as $gateway ) {
				if ( in_array( $current_section, array( $gateway->id, sanitize_title( get_class( $gateway ) ) ), true ) ) {
					do_action( 'woocommerce_update_options_payment_gateways_' . $gateway->id );
					$wc_payment_gateways->init();
				}
			}

			$this->do_update_options_action();
		}
	}
}

return new WC_Settings_Payment_Gateways();
